// 业务平台/卡户人信息查询/卡片查询
import { FC, useState, memo, useEffect } from 'react';
import _ from 'lodash';
import { PageTemplate } from '@/components';
import { OPERATE_TYPE } from '@/constants/publicConstant';
import { ISearchSource } from '@/types/ICommon';
import { pageConfig, dictEnum } from './pageConfig';
import { IDetailData } from './ICardQuery';
import Detail from './Detail';

// 查询重置数据
const resetValue = { keyValue: 'idNo', idNo: '', crcdCardholderNo: '', crcdNo: '' };

const CardQuery: FC = () => {
  const { urlObj, prefix, searchSource: searchSourceInit, columns, cardTitle, optionList } = pageConfig;
  // hook变量
  const [detailData, setDetailData] = useState<IDetailData | object>({});
  const [searchSource, setSearchSource] = useState(searchSourceInit);

  useEffect(() => {
    toggleSelectSource(resetValue);
  }, []);

  // 搜索框根据所选类型展示不同的下拉框
  const toggleSelectSource = ({ keyValue }) => {
    if (!keyValue) {
      return;
    }
    const searchSourceTemp: ISearchSource[] = [];
    for (const item of searchSourceInit) {
      const res: ISearchSource = { ...item };
      delete res.hide;
      // 是否有hide字段或展示类型所选的输入框
      (_.isNil(item.hide) || item.value === keyValue) && searchSourceTemp.push(res);
    }
    setSearchSource(searchSourceTemp);
  };
  // 列表按钮操作
  const handleAction = async (editType: string, row: any) => {
    if (editType === OPERATE_TYPE.detail) {
      const { detailList } = row;
      typeof detailList === 'object' && setDetailData(detailList);
    } else {
      setDetailData({});
    }
  };
  // 搜索框改变事件
  const handleSearchChange = (changedValues) => {
    // 改变类型才处理
    if (changedValues.value || changedValues.idNo || changedValues.crcdNo || changedValues.crcdCardholderNo) {
      return;
    }
    toggleSelectSource(changedValues);
  };

  // 查询条件配置
  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
    props: { onChange: handleSearchChange, onRest: () => toggleSelectSource(resetValue) },
  };
  // 表格配置
  const tableConfig = {
    columns,
    rowKey: 'paramIndex',
    optionList,
    showPagination: false,
  };
  // 表单配置
  const formConfig = {
    isCustom: true,
    customChildren: <Detail data={detailData} />,
  };
  // 表格操作栏配置
  const formActionConfig = {
    showCreate: false,
  };

  return (
    <PageTemplate
      searchConfig={searchConfig}
      tableConfig={tableConfig}
      formConfig={formConfig}
      formActionConfig={formActionConfig}
      urlObj={urlObj}
      cardTitle={cardTitle}
      intlPrefix={prefix}
      dictEnum={dictEnum}
      onAction={handleAction}
    />
  );
};

export default memo(CardQuery);
