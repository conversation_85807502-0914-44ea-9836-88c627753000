import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, FORMITEM_TYPE, I18N_COMON_PAGENAME } from '@/constants/publicConstant';

// 详情配置
export default [
  {
    key: 'baseInfo',
    prefix: I18N_COMON_PAGENAME.COMMON,
    data: [
      {
        type: FORMITEM_TYPE.FormHearder,
        title: 'baseInfo',
        prefix: I18N_COMON_PAGENAME.COMMON,
      },
      {
        type: FORMITEM_TYPE.Row,
        data: [
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'crcdOrgNo',
            label: 'crcdOrgNo',

            prefix: I18N_COMON_PAGENAME.COMMON,
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardPrinCardholderOrgNo',
            label: 'cardPrinCardholderOrgNo',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardSuplCardholderOrgNo',
            label: 'cardSuplCardholderOrgNo',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'crcdNo',
            label: 'crcdNo',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardPrinCardholderNo',
            label: 'cardPrinCardholderNo',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardSuplCardholderNo',
            label: 'cardSuplCardholderNo',
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'corpOrgNo',
            label: 'corpOrgNo',
            data: DICT_CONSTANTS.CORP_OGR_LIST,
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'suplCardFlag',
            label: 'suplCardFlag',
            data: DICT_CONSTANTS.SUPL_CARD_FLAG_LIST,
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'suplCardRelationship',
            label: 'suplCardRelationship',
            data: DICT_CONSTANTS.SUPL_CARD_RELATIONSHIP_LIST,
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'corpCardholderNo',
            label: 'corpCardholderNo',
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'crcdSts',
            label: 'crcdSts',
            data: DICT_CONSTANTS.CRCD_STS_LIST,
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'crcdCardStsChgDate',
            label: 'cardStsChgDate',
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'cardCcy',
            label: 'crcdCcy',
            data: DICT_CONSTANTS.CCY_LIST,
            prefix: I18N_COMON_PAGENAME.COMMON,
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'cardType',
            label: 'cardType',
            data: DICT_CONSTANTS.CARD_TYPE_LIST,
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardProductId',
            label: 'cardProductParmId',
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'cardLevel',
            label: 'cardLevel',

            data: DICT_CONSTANTS.CARD_LEVEL_LSIT,
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardFaceId',
            label: 'cardFaceId',
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'specProductFlag',
            label: 'specProductFlag',
            data: DICT_CONSTANTS.SPEC_PRODUCT_FLAG_LIST,
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'crcdExpireDate',
            label: 'crcdExpireDate',
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'cardOpenDate',
            label: 'cardOpenDate',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cycleDay',
            label: 'cycleDay',
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'crcdPrevExpireDate',
            label: 'crcdPrevExpireDate',
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'cardCloseDate',
            label: 'cardCloseDate',
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'prevStmtDate',
            label: 'prevStmtDate',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'suretyId',
            label: 'suretyId',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardholderContactInfo1',
            label: 'cardholderContactInfo1',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardAlphaKey',
            label: 'cardAlphaKey',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'savingsAcct',
            label: 'savingsAcct',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardholderContactInfo2',
            label: 'cardholderContactInfo2',
          },
        ],
      },
    ],
  },
  {
    key: 'cardOpeningAndActivInfo',
    data: [
      {
        type: FORMITEM_TYPE.FormHearder,
        title: 'cardOpeningAndActivInfo',
      },
      {
        type: FORMITEM_TYPE.Row,
        data: [
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'chanCode',
            label: 'chanCode',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cmpnPerslEmpNo',
            label: 'cmpnPerslEmpNo',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cmpnPerslName',
            label: 'cmpnPerslName',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'chanTypeCode',
            label: 'chanTypeCode',
          },
          // 占位
          {},
          {},
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'cardTrxRangeFlag',
            label: 'trxRangeFlag',
            data: DICT_CONSTANTS.CARD_TRX_RANGE_FLAG_LSIT,
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'cardActiveDate',
            label: 'cardActiveDate',
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'cardActiveMethod',
            label: 'cardActiveMethod',
            data: DICT_CONSTANTS.CARD_ACTIVE_METHOD_LIST,
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'cardActiveSts',
            label: 'cardActiveSts',

            data: DICT_CONSTANTS.CARD_ACTIVE_STS_LSIT,
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'cardPrevActiveDate',
            label: 'cardPrevActiveDate',
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'cardActiveChanel',
            label: 'cardActiveChanel',
            data: DICT_CONSTANTS.CARD_ACTIVE_CHANEL_LIST,
          },
        ],
      },
    ],
  },
  {
    key: 'blockCodeAndAuthInfo',
    data: [
      {
        type: FORMITEM_TYPE.FormHearder,
        title: 'blockCodeAndAuthInfo',
      },
      {
        type: FORMITEM_TYPE.Row,
        data: [
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardBlockCode',
            label: 'cardBlockCode',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardBlockCodeMemo',
            label: 'cardBlockCodeMemo',
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'cardBlockCodeMaintDate',
            label: 'cardBlockCodeMaintDate',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardPrevBlockCode',
            label: 'cardPrevBlockCode',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardPrevBlockCodeMemo',
            label: 'cardPrevBlockCodeMemo',
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'cardPrevBlockCodeBeginDate',
            label: 'cardPrevBlockCodeBeginDate',
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'cardPrevBlockCodeEndDate',
            label: 'cardPrevBlockCodeEndDate',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardReplaceBlockCode',
            label: 'cardReplaceBlockCode',
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'cardChgOffRsn',
            label: 'cardChgOffRsn',
            data: DICT_CONSTANTS.CARD_CHG_OFF_RSN_LIST,
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'cardChgOffDate',
            label: 'cardChgOffDate',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardPinRetriesCnt',
            label: 'cardPinRetriesCnt',
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'cardPinRetriesDate',
            label: 'cardPinRetriesDate',
          },
        ],
      },
    ],
  },
  {
    key: 'cardMailingInfo',
    data: [
      {
        type: FORMITEM_TYPE.FormHearder,
        title: 'cardMailingInfo',
      },
      {
        type: FORMITEM_TYPE.Row,
        data: [
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'issueActionFlag',
            label: 'issueActionFlag',
            data: DICT_CONSTANTS.ISSUE_ACTION_FLAG_ENUM,
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'issueReqAcc',
            label: 'issueReqAcc',
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'issueDate',
            label: 'issueDate',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'issuedCardCount',
            label: 'issuedCardCount',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'nmbrCards',
            label: 'nmbrCards',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'nmbrCardsRet',
            label: 'nmbrCardsRet',
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'prevIssueActionFlag',
            label: 'prevIssueActionFlag',
            data: DICT_CONSTANTS.ISSUE_ACTION_FLAG_ENUM,
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'prevIssueActionDate',
            label: 'prevIssueActionDate',
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'prevMakeCardDate',
            label: 'prevMakeCardDate',
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'cardMed',
            label: 'cardMed',

            data: DICT_CONSTANTS.CARD_MED_LIST,
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'prevCardMed',
            label: 'prevCardMed',
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'ownershipFlag',
            label: 'ownershipFlag',
            data: DICT_CONSTANTS.OWNERSHIP_FLAG_ENUM,
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'partnerMemNo',
            label: 'partnerMemNo',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'coBraCardPartnerNo',
            label: 'coBraCardPartnerNo',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'agentBankNmbr',
            label: 'agentBankNmbr',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardShortName',
            label: 'cardShortName',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'embossName',
            label: 'embossName',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardApplySeq',
            label: 'cardApplySeq',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'vpaCardSpecailSeq',
            label: 'vpaCardSpecailSeq',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'vpaCardMchVipCode',
            label: 'vpaCardMchVipCode',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'vpaCardMchVipId',
            label: 'vpaCardMchVipId',
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'vpaCardOpenDate',
            label: 'vpaCardOpenDate',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'vpaCardOpenChanel',
            label: 'vpaCardOpenChanel',
          },
        ],
      },
    ],
  },
  {
    key: 'cardReplaceAndCancelInfo',
    data: [
      {
        type: FORMITEM_TYPE.FormHearder,
        title: 'cardReplaceAndCancelInfo',
      },
      {
        type: FORMITEM_TYPE.Row,
        data: [
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'txfrEffDate',
            label: 'txfrEffDate',
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'cardUpgradeFlag',
            label: 'cardUpgradeFlag',
            data: DICT_CONSTANTS.CARD_UPGRADE_FLAG_ENUM,
          },
          // 占位
          {},
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'chgCardNewCardNo',
            label: 'chgCardNewCardNo',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'chgCardNewCardCcy',
            label: 'chgCardNewCardCcy',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'chgCardNewCardProductNo',
            label: 'chgCardNewCardProductNo',
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'cancelCardExpireDate',
            label: 'cancelCardExpireDate',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cancelCardPerslEmpNo',
            label: 'cancelCardPerslEmpNo',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cancelCardPerslName',
            label: 'cancelCardPerslName',
          },
        ],
      },
    ],
  },
  {
    key: 'cardFeeParama',
    data: [
      {
        type: FORMITEM_TYPE.FormHearder,
        title: 'cardFeeParama',
      },
      {
        type: FORMITEM_TYPE.Row,
        data: [
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'waiveAnnualFeeFlag',
            label: 'waiveAnnualFeeFlag',
            data: DICT_CONSTANTS.WAIVE_ANNUAL_FEE_FLAG_LIST,
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'cardAnnualFeeFlag',
            label: 'cardAnnualFeeFlag',
            data: DICT_CONSTANTS.CARD_ANNUAL_FEE_FLAG_LIST,
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'waiveJoinFeeFlag',
            label: 'waiveJoinFeeFlag',
            data: DICT_CONSTANTS.WAIVE_JOIN_FEE_FLAG_ENUM,
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'annualFee',
            label: 'annualFee',
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'annualFeeDate',
            label: 'annualFeeDate',
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'firstAnnualFeeDate',
            label: 'firstAnnualFeeDate',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardFeeParam',
            label: 'cardFeeParam',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardProcParam',
            label: 'cardProcParam',
          },
        ],
      },
    ],
  },
  {
    key: 'additional',
    data: [
      {
        type: FORMITEM_TYPE.FormHearder,
        title: 'additional',
      },
      {
        type: FORMITEM_TYPE.Row,
        data: [
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'enableAutoLoadFlag',
            label: 'enableAutoLoadFlag',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'preReissueActionFlag',
            label: 'preReissueActionFlag',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'easycardSignFlag',
            label: 'easycardSignFlag',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'autoLoadThold',
            label: 'autoLoadThold',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'reissueActionFlag',
            label: 'reissueActionFlag',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'ivrPinSetFlag',
            label: 'ivrPinSetFlag',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'changePinFlag',
            label: 'changePinFlag',
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'officer',
            label: 'officer',
            data: DICT_CONSTANTS.OFFICER_LIST,
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'insCode',
            label: 'insCode',
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'firstUsgFlag',
            label: 'firstUsgFlag',
            data: DICT_CONSTANTS.FIRST_USG_FLAG_LIST,
            isint: '0',
          },
          {
            type: COMPONENT_TYPE.SELECT,
            name: 'affinityFlag',
            label: 'affinityFlag',
            data: DICT_CONSTANTS.AFFINITY_FLAG_LIST,
            isint: '0',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardUsageFlag',
            label: 'cardUsageFlag',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'regionCodes',
            label: 'regionCodes',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'exceptionResponseCode',
            label: 'exceptionResponseCode',
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'dteWarningBulletin',
            label: 'dteWarningBulletin',
          },
          {
            type: COMPONENT_TYPE.DATE_PICKER,
            name: 'dteExceptionPurge',
            label: 'dteExceptionPurge',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardMemo1',
            label: 'cardMemo1',
          },
          {
            type: COMPONENT_TYPE.INPUT,
            name: 'cardMemo2',
            label: 'cardMemo2',
          },
        ],
      },
    ],
  },
];
