import DICT_CONSTANTS from '@/constants/dictConstants';
import PERMISSION_CONSTANTS from '@/constants/permissionConstants';
import { COMPONENT_TYPE, I18N_COMON_PAGENAME, OPERATE_TYPE, RENDER_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { getCardInfoList } from '@/services/custCardQuery';
import { IPageConfig } from '@/types/ICommon';
import { IDetailData } from './ICardQuery';

// 需要查询的参数类型
export const dictEnum = {
  crcdOrgNo: DICT_CONSTANTS.DICT_ENUM_MAP.crcdOrgNo,
};

export const pageConfig: IPageConfig = {
  // 国际化前缀
  prefix: I18N_COMON_PAGENAME.CARD_MODELE,
  // 页面标题
  cardTitle: 'cardQuery',
  // 页面接口请求
  urlObj: {
    detail: urlConstants.CARD_QUERY.DETAIL,
    getRequestData: (postData: any, type, rowData: IDetailData): object => {
      if (type === OPERATE_TYPE.detail) {
        const { url } = postData;
        const { crcdNo, crcdExpireDate } = rowData;
        return { url, searchValue: { crcdNo, crcdExpireDate } };
      }
      return { ...postData };
    },
    requestFuncMap: {
      list: getCardInfoList,
    },
  },
  // 查询条件字段
  searchSource: [
    {
      value: 'keyValue',
      label: 'searchType',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.SEARCH_SOURCE_LIST,
      showKey: false, // 多选框不展示key
      rules: [{ required: true }],
      ref: 'value',
      allowClear: false,
      prefix: I18N_COMON_PAGENAME.LIMIT_PARAM,
    },
    {
      hide: false,
      value: 'idNo',
      label: 'idNo',
      type: COMPONENT_TYPE.INPUT,
      rules: [{ required: true }],
      prefix: I18N_COMON_PAGENAME.LIMIT_PARAM,
      maxLength: 12,
      showCount: true,
      width: 220,
    },
    {
      hide: true,
      value: 'crcdNo',
      label: 'crcdNo',
      type: COMPONENT_TYPE.INPUT,
      rules: [{ required: true }],
      prefix: I18N_COMON_PAGENAME.LIMIT_PARAM,
      maxLength: 16,
      showCount: true,
      width: 220,
    },
    {
      hide: true,
      value: 'crcdCardholderNo',
      label: 'crcdCardholderNo',
      type: COMPONENT_TYPE.INPUT,
      rules: [{ required: true }],
      prefix: I18N_COMON_PAGENAME.LIMIT_PARAM,
      maxLength: 16,
      showCount: true,
      width: 220,
    },
  ],
  // 列表字段
  columns: [
    {
      key: 'paramIndex',
      dataIndex: 'paramIndex',
      title: 'paramIndex',
      prefix: I18N_COMON_PAGENAME.COMMON,
      width: 70,
    },
    {
      key: 'crcdNo',
      dataIndex: 'crcdNo',
      title: 'crcdNo',
      width: 130,
    },
    {
      key: 'suplCardFlag',
      dataIndex: 'suplCardFlag',
      title: 'suplCardFlag',
      width: 120,
      valueType: RENDER_TYPE.MockDictionary,
      data: DICT_CONSTANTS.SUPL_CARD_FLAG_LIST,
    },
    {
      key: 'cardProductParmId',
      dataIndex: 'cardProductParmId',
      title: 'cardProductParmId',
      width: 150,
    },
    {
      key: 'crcdAcctNo',
      dataIndex: 'crcdAcctNo',
      title: 'crcdAcctNo',
      width: 180,
      prefix: I18N_COMON_PAGENAME.AUTH_MODULE,
    },
    {
      key: 'acctSeqNo',
      dataIndex: 'acctSeqNo',
      title: 'crcdAcctSeqNo',
      width: 180,
      prefix: I18N_COMON_PAGENAME.AUTH_MODULE,
    },
    {
      key: 'acctCcy',
      dataIndex: 'acctCcy',
      title: 'crcdAcctCcy',
      width: 180,
      prefix: I18N_COMON_PAGENAME.LIMIT_PARAM,
      valueType: RENDER_TYPE.MockDictionary,
      data: DICT_CONSTANTS.CCY_LIST,
    },
    {
      key: 'cardActiveSts',
      dataIndex: 'cardActiveSts',
      title: 'cardActiveSts',
      width: 200,
      valueType: RENDER_TYPE.MockDictionary,
      data: DICT_CONSTANTS.CARD_ACTIVE_STS_LSIT,
    },
    {
      key: 'cardBlockCode',
      dataIndex: 'cardBlockCode',
      title: 'cardBlockCode',
      width: 150,
      prefix: I18N_COMON_PAGENAME.AUTH_MODULE,
    },
    {
      key: 'trxRangeFlag',
      dataIndex: 'trxRangeFlag',
      title: 'trxRangeFlag',
      width: 150,
      valueType: RENDER_TYPE.MockDictionary,
      data: DICT_CONSTANTS.CARD_TRX_RANGE_FLAG_LSIT,
    },
    {
      key: 'crcdExpireDate',
      dataIndex: 'crcdExpireDate',
      title: 'crcdExpireDate',
      width: 150,
      valueType: RENDER_TYPE.Date,
    },
    {
      key: 'crcdSts',
      dataIndex: 'crcdSts',
      title: 'crcdSts',
      width: 150,
      valueType: RENDER_TYPE.MockDictionary,
      data: DICT_CONSTANTS.CRCD_STS_LIST,
    },
  ],
  // 操作列配置
  optionList: [{ type: OPERATE_TYPE.detail, permissionId: PERMISSION_CONSTANTS.CARD_QUERY.DETAIL }],
};
