// 业务平台/卡户人信息查询/卡片查询/详情
import { FC, useEffect, useState, ReactNode } from 'react';
import { Anchor } from 'antd';
import type { AnchorLinkItemProps } from 'antd/lib/anchor/Anchor';
import FormTemplate from '@/components/form/FormTemplate';
import useIntlCustom from '@/hooks/useIntlCustom';
import { IDetailProps } from './ICardQuery';
import { pageConfig } from './pageConfig';
import detailConfig from './detailConfig';
import styles from './index.module.css';

const { prefix } = pageConfig;

const Detail: FC<IDetailProps> = ({ data }) => {
  const [anchorItems, setAnchorItems] = useState<AnchorLinkItemProps[]>();
  const { translate } = useIntlCustom();

  useEffect(() => {
    // 设置锚点配置
    setAnchorItems(
      detailConfig.map((item) => ({
        key: item.key,
        href: `#${item.key}`,
        title: translate(item.prefix || prefix, item.key),
      })),
    );
  }, []);

  // 渲染组件
  const renderChildren = (): ReactNode => {
    return detailConfig.map((item) => {
      return (
        <FormTemplate
          key={item.key}
          id={item.key}
          config={item.data}
          initialData={data}
          canEdit={false}
          showMaintenance={false}
          intlPrefix={prefix}
          colSpan={7}
        />
      );
    });
  };

  // 获取锚点的父组件，使锚点随页面滚动而滚动
  const getContainer = (): any => {
    return document.getElementsByClassName('ant-card-body')[0];
  };

  // 锚点点击事件（不记录历史）
  const handleClick = (e) => {
    e.preventDefault();
  };

  return (
    <>
      <div className={styles.anchorWrapper}>
        <Anchor
          affix={false}
          direction="horizontal"
          items={anchorItems}
          getContainer={getContainer}
          onClick={handleClick}
        />
      </div>
      {renderChildren()}
    </>
  );
};

export default Detail;
