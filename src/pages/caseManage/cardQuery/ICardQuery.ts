import { IUnknownProperties } from '@/types/ICommon';

// 详情字段约束接口
export interface IDetailData extends IUnknownProperties {
  /**
   * 序号
   */
  paramIndex: string;
  /**
   * 机构号
   */
  crcdOrgNo: string | null;
  /**
   * 主附卡標識
   */
  suplCardFlag: string;
  /**
   * 卡產品
   */
  cardProductParmId: string;
  /**
   * 信用卡帐户号
   */
  crcdAcctNo: string;
  /**
   * 信用卡帐户序号
   */
  acctSeqNo: string;
  /**
   * 信用卡帐户币种
   */
  acctCcy: string;
  /**
   * 卡片啟動狀態
   */
  cardActiveSts: string;
  /**
   * 卡片封锁码
   */
  cardBlockCode: string;
  /**
   * 卡片交易範圍標識
   */
  trxRangeFlag: string;
  /**
   * 信用卡卡片有效期
   */
  crcdExpireDate: string;
  /**
   * 信用卡卡片状态
   */
  crcdSts: string;
}

// 详情props约束接口
export interface IDetailProps {
  data: IDetailData | object;
}