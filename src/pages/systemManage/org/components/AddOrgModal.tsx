import useIntlCustom from '@/hooks/useIntlCustom';
import { Form, Input, Modal, Select } from 'antd';
import { FC, useMemo } from 'react';
import { PARENT_ORG_OPTIONS } from '../constants';

interface AddOrgModalProps {
  visible: boolean;
  orgParentDetail: any;
  onCancel: () => void;
  onOk: (values: AddOrgFormValues) => void;
  loading: boolean;
  prefix: string;
}

export interface AddOrgFormValues {
  id: string;
  name: string;
  parent: string;
  orgParentDetail: any;
}

const AddOrgModal: FC<AddOrgModalProps> = ({ visible, onCancel, onOk, orgParentDetail, loading, prefix }) => {
  const [form] = Form.useForm<AddOrgFormValues>();
  const { translate } = useIntlCustom();

  const parentOrgOptions = useMemo(
    () => PARENT_ORG_OPTIONS.map((opt) => ({ ...opt, label: translate(prefix, opt.label) })),
    [translate, prefix],
  );

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      await onOk(values);
      // 成功后重置表单
      form.resetFields();
    } catch (error) {
      // 验证失败不做处理
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={translate(prefix, 'addOrg')}
      open={visible}
      onCancel={handleCancel}
      onOk={handleOk}
      confirmLoading={loading}
    >
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={{
          parent: orgParentDetail?.key,
        }}
      >
        <Form.Item
          label={translate(prefix, 'orgId')}
          name="id"
          rules={[{ required: true, message: translate(prefix, 'orgIdRequired') }]}
        >
          <Input placeholder={translate(prefix, 'orgIdPlaceholder')} />
        </Form.Item>
        <Form.Item
          label={translate(prefix, 'orgName')}
          name="name"
          rules={[{ required: true, message: translate(prefix, 'orgNameRequired') }]}
        >
          <Input placeholder={translate(prefix, 'orgNamePlaceholder')} />
        </Form.Item>
        <Form.Item
          label={translate(prefix, 'parentOrg')}
          name="parent"
          rules={[{ required: true, message: translate(prefix, 'parentOrgRequired') }]}
        >
          <Select placeholder={translate(prefix, 'parentOrgPlaceholder')} disabled={orgParentDetail?.key}>
            {parentOrgOptions.map((opt) => (
              <Select.Option key={opt.value} value={opt.value}>
                {opt.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddOrgModal;
